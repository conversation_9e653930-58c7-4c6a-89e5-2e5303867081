import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import ProductForm from '../ProductForm'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import sleep from '@/utils/sleep'
import { TbTrash } from 'react-icons/tb'
import type { ProductFormSchema } from '../ProductForm/types'
import type { Consumables } from '../types'

type ConsumableModalProps = {
    isOpen: boolean
    onClose: () => void
    editData?: Consumables
    onSuccess?: () => void
}

const ConsumableModal = ({
    isOpen,
    onClose,
    editData,
    onSuccess,
}: ConsumableModalProps) => {
    const [discardConfirmationOpen, setDiscardConfirmationOpen] =
        useState(false)
    const [isSubmiting, setIsSubmiting] = useState(false)

    const isEdit = !!editData

    const handleFormSubmit = async (values: ProductFormSchema) => {
        console.log('Submitted values', values)
        setIsSubmiting(true)
        await sleep(800)
        setIsSubmiting(false)
        
        toast.push(
            <Notification type="success">
                {isEdit ? 'Consumable updated!' : 'Consumable created!'}
            </Notification>,
            { placement: 'top-center' },
        )
        
        onSuccess?.()
        onClose()
    }

    const handleConfirmDiscard = () => {
        setDiscardConfirmationOpen(false)
        toast.push(
            <Notification type="success">Consumable discarded!</Notification>,
            { placement: 'top-center' },
        )
        onClose()
    }

    const handleDiscard = () => {
        setDiscardConfirmationOpen(true)
    }

    const handleCancel = () => {
        setDiscardConfirmationOpen(false)
    }

    const getDefaultValues = (): ProductFormSchema => {
        if (isEdit && editData) {
            return {
                name: editData.description || '',
                description: editData.description || '',
                productCode: editData.id || '',
                taxRate: 0,
                price: '',
                bulkDiscountPrice: '',
                costPerItem: '',
                imgList: [],
                category: editData.category || '',
                tags: [],
                brand: '',
            }
        }
        
        return {
            name: '',
            description: '',
            productCode: '',
            taxRate: 0,
            price: '',
            bulkDiscountPrice: '',
            costPerItem: '',
            imgList: [],
            category: '',
            tags: [],
            brand: '',
        }
    }

    return (
        <>
            <Dialog
                isOpen={isOpen}
                onClose={onClose}
                onRequestClose={onClose}
                shouldCloseOnOverlayClick={false}
                shouldCloseOnEsc={false}
                width={1200}
                height={800}
            >
                <div className="flex flex-col h-full">
                    <div className="flex items-center justify-between p-6 border-b">
                        <h3 className="text-lg font-semibold">
                            {isEdit ? 'Edit Consumable' : 'Add New Consumable'}
                        </h3>
                    </div>
                    
                    <div className="flex-1 overflow-auto">
                        <ProductForm
                            newProduct={!isEdit}
                            defaultValues={getDefaultValues()}
                            onFormSubmit={handleFormSubmit}
                        >
                            <div className="flex items-center justify-between px-8 py-4 border-t bg-gray-50 dark:bg-gray-800">
                                <span></span>
                                <div className="flex items-center gap-3">
                                    <Button
                                        type="button"
                                        customColorClass={() =>
                                            'border-error ring-1 ring-error text-error hover:border-error hover:ring-error hover:text-error bg-transparent'
                                        }
                                        icon={<TbTrash />}
                                        onClick={handleDiscard}
                                    >
                                        Discard
                                    </Button>
                                    <Button
                                        type="button"
                                        onClick={onClose}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        variant="solid"
                                        type="submit"
                                        loading={isSubmiting}
                                    >
                                        {isEdit ? 'Update' : 'Create'}
                                    </Button>
                                </div>
                            </div>
                        </ProductForm>
                    </div>
                </div>
            </Dialog>
            
            <ConfirmDialog
                isOpen={discardConfirmationOpen}
                type="danger"
                title="Discard changes"
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDiscard}
            >
                <p>
                    Are you sure you want to discard this? This action can&apos;t
                    be undone.
                </p>
            </ConfirmDialog>
        </>
    )
}

export default ConsumableModal
