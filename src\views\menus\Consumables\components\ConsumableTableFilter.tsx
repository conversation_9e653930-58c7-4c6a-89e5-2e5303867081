import React, { useState } from 'react'
import Button from '@/components/ui/Button'
import Drawer from '@/components/ui/Drawer'
import Checkbox from '@/components/ui/Checkbox'
import Badge from '@/components/ui/Badge'
import Select, { Option as DefaultOption } from '@/components/ui/Select'
import { components } from 'react-select'
import { Form, FormItem } from '@/components/ui/Form'
import useConsumableList from '../hooks/useConsumableList'
import { TbFilter } from 'react-icons/tb'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import type { ZodType } from 'zod'
import type { ControlProps, OptionProps } from 'react-select'
import classNames from '@/utils/classNames'

type FormSchema = {
    consumableStatus: string
    consumableType: string[]
    visibleColumns: string[]
}

type Option = {
    value: string
    label: string
    className: string
}

const consumableStatusOption: Option[] = [
    { value: 'معتمد', label: 'معتمد', className: 'bg-emerald-500' },
    { value: 'غير معتمد', label: 'غير معتمد', className: 'bg-red-500' },
]

const consumableTypeList = [
    { value: 'صناديق', label: 'صناديق' },
    { value: 'أكياس', label: 'أكياس' },
    { value: 'أفيز', label: 'أفيز' },
]

const availableColumns = [
    { value: 'category', label: 'Type' },
    { value: 'description', label: 'Description' },
    { value: 'count', label: 'N' },
    { value: 'wareHouse', label: 'Warehouse' },
    { value: 'status', label: 'Status' },
    { value: 'assignedBy', label: 'Assigned By' },
    { value: 'attachments', label: 'Attachments' },
    { value: 'createdAt', label: 'Created At' },
]

const CustomSelectOption = (props: OptionProps<Option>) => {
    return (
        <DefaultOption<Option>
            {...props}
            customLabel={(data, label) => (
                <span className="flex items-center gap-2">
                    <Badge className={data.className} />
                    <span className="ml-2 rtl:mr-2">{label}</span>
                </span>
            )}
        />
    )
}

const CustomControl = ({ children, ...props }: ControlProps<Option>) => {
    const selected = props.getValue()[0]
    const ControlComponent = components.Control as React.ComponentType<
        ControlProps<Option>
    >
    return (
        <ControlComponent {...props}>
            {selected && (
                <Badge className={classNames('ml-4', selected.className)} />
            )}
            {children}
        </ControlComponent>
    )
}

const validationSchema: ZodType<FormSchema> = z.object({
    consumableStatus: z.string(),
    consumableType: z.array(z.string()),
    visibleColumns: z.array(z.string()),
})

const ConsumableTableFilter = () => {
    const [filterIsOpen, setFilterIsOpen] = useState(false)

    const { filterData, setFilterData } = useConsumableList()

    const { handleSubmit, control } = useForm<FormSchema>({
        defaultValues: filterData,
        resolver: zodResolver(validationSchema),
    })

    const onSubmit = (values: FormSchema) => {
        setFilterData(values)
        setFilterIsOpen(false)
    }

    return (
        <>
            <Button icon={<TbFilter />} onClick={() => setFilterIsOpen(true)}>
                Filter
            </Button>
            <Drawer
                title="Filter"
                isOpen={filterIsOpen}
                onClose={() => setFilterIsOpen(false)}
                onRequestClose={() => setFilterIsOpen(false)}
            >
                <Form
                    className="h-full"
                    containerClassName="flex flex-col justify-between h-full"
                    onSubmit={handleSubmit(onSubmit)}
                >
                    <div>
                        <FormItem label="Consumable status">
                            <Controller
                                name="consumableStatus"
                                control={control}
                                render={({ field }) => (
                                    <Select<Option>
                                        options={consumableStatusOption}
                                        {...field}
                                        value={consumableStatusOption.filter(
                                            (option) =>
                                                option.value === field.value,
                                        )}
                                        components={{
                                            Option: CustomSelectOption,
                                            Control: CustomControl,
                                        }}
                                        onChange={(option) =>
                                            field.onChange(option?.value)
                                        }
                                    />
                                )}
                            />
                        </FormItem>
                        <FormItem label="Consumable type">
                            <div className="mt-4">
                                <Controller
                                    name="consumableType"
                                    control={control}
                                    render={({ field }) => (
                                        <Checkbox.Group
                                            className="flex"
                                            {...field}
                                        >
                                            {consumableTypeList.map((type) => (
                                                <Checkbox
                                                    key={type.value}
                                                    name={field.name}
                                                    value={type.value}
                                                    className="justify-between flex-row-reverse heading-text"
                                                >
                                                    {type.label}
                                                </Checkbox>
                                            ))}
                                        </Checkbox.Group>
                                    )}
                                />
                            </div>
                        </FormItem>
                        <FormItem label="Customize Columns">
                            <div className="mt-4">
                                <Controller
                                    name="visibleColumns"
                                    control={control}
                                    render={({ field }) => (
                                        <Checkbox.Group
                                            vertical
                                            className="flex"
                                            {...field}
                                        >
                                            {availableColumns.map((column) => (
                                                <Checkbox
                                                    key={column.value}
                                                    name={field.name}
                                                    value={column.value}
                                                    className="justify-between flex-row-reverse heading-text"
                                                >
                                                    {column.label}
                                                </Checkbox>
                                            ))}
                                        </Checkbox.Group>
                                    )}
                                />
                            </div>
                        </FormItem>
                    </div>
                    <Button variant="solid" type="submit">
                        Query
                    </Button>
                </Form>
            </Drawer>
        </>
    )
}

export default ConsumableTableFilter
