import { useEffect } from 'react'
import { Form } from '@/components/ui/Form'
import Container from '@/components/shared/Container'
import BottomStickyBar from '@/components/template/BottomStickyBar'
import GeneralSection from './components/GeneralSection'
import PricingSection from './components/PricingSection'
import AttributeSection from './components/AttributeSection'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import isEmpty from 'lodash/isEmpty'
import type { Consumable } from './types'
import type { ZodType } from 'zod'
import type { CommonProps } from '@/@types/common'

type ConsumableFormProps = {
    onFormSubmit: (values: Consumable) => void
    defaultValues?: Consumable
    newConsumable?: boolean
} & CommonProps

const validationSchema: ZodType<Consumable> = z.object({
    category: z.string().min(1, { message: 'Category name required!' }),
    count: z.union([z.string(), z.number()], {
        errorMap: () => ({ message: 'count required!' }),
    }),
    description: z.string().min(1, { message: 'Description required!' }),
    wareHouse: z.string().min(1, { message: 'WareHouse required!' }),
    status: z.string().min(1, { message: 'Status required!' }),
    attachments: z.string().min(1, { message: 'Attachments required!' }),
})

const ConsumableForm = (props: ConsumableFormProps) => {
    const { onFormSubmit, defaultValues = {}, children } = props

    const {
        handleSubmit,
        reset,
        formState: { errors },
        control,
    } = useForm<Consumable>({
        defaultValues: {
            ...defaultValues,
        },
        resolver: zodResolver(validationSchema),
    })

    useEffect(() => {
        if (!isEmpty(defaultValues)) {
            reset(defaultValues)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [JSON.stringify(defaultValues)])

    const onSubmit = (values: Consumable) => {
        onFormSubmit?.(values)
    }

    return (
        <Form
            className="flex w-full h-full"
            containerClassName="flex flex-col w-full justify-between"
            onSubmit={handleSubmit(onSubmit)}
        >
            <Container>
                <div className="flex flex-col xl:flex-row gap-4">
                    <div className="gap-4 flex flex-col flex-auto">
                        <GeneralSection control={control} errors={errors} />
                        <PricingSection control={control} errors={errors} />
                    </div>
                    <div className="lg:min-w-[440px] 2xl:w-[500px] gap-4 flex flex-col">
                        <AttributeSection control={control} errors={errors} />
                    </div>
                </div>
            </Container>
            <BottomStickyBar>{children}</BottomStickyBar>
        </Form>
    )
}

export default ConsumableForm
