export type Consumables = {
    id: string
    category: string
    count: number
    description: string
    wareHouse: string
    status: string
    attachments: string
    assignedBy: string
    createdAt: string
}

export type Filter = {
    consumableStatus: string
    consumableType: string[]
    visibleColumns: string[]
}

export type GetConsumableListResponse = {
    list: Consumables[]
    total: number
}
