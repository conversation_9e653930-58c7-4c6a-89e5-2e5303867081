import { create } from 'zustand'
import type { TableQueries } from '@/@types/common'
import type { Consumables, Filter } from '../types'

export const initialTableData: TableQueries = {
    pageIndex: 1,
    pageSize: 10,
    query: '',
    sort: {
        order: '',
        key: '',
    },
}

export const initialFilterData = {
    consumableStatus: 'معتمد',
    consumableType: ['صناديق', 'أكياس', 'أفيز'],
    visibleColumns: [
        'category',
        'description',
        'count',
        'wareHouse',
        'status',
        'assignedBy',
        'action',
    ],
}

export type ConsumablesListState = {
    tableData: TableQueries
    filterData: Filter
    selectedConsumable: Partial<Consumables>[]
}

type ConsumablesListAction = {
    setFilterData: (payload: Filter) => void
    setTableData: (payload: TableQueries) => void
    setSelectedConsumable: (checked: boolean, customer: Consumables) => void
    setSelectAllConsumable: (customer: Consumables[]) => void
}

const initialState: ConsumablesListState = {
    tableData: initialTableData,
    filterData: initialFilterData,
    selectedConsumable: [],
}

export const useConsumableListStore = create<
    ConsumablesListState & ConsumablesListAction
>((set) => ({
    ...initialState,
    setFilterData: (payload) => set(() => ({ filterData: payload })),
    setTableData: (payload) => set(() => ({ tableData: payload })),
    setSelectedConsumable: (checked, row) =>
        set((state) => {
            const prevData = state.selectedConsumable
            if (checked) {
                return { selectedConsumable: [...prevData, ...[row]] }
            } else {
                if (
                    prevData.some(
                        (prevConsumable) => row.id === prevConsumable.id,
                    )
                ) {
                    return {
                        selectedConsumable: prevData.filter(
                            (prevConsumable) => prevConsumable.id !== row.id,
                        ),
                    }
                }
                return { selectedConsumable: prevData }
            }
        }),
    setSelectAllConsumable: (row) => set(() => ({ selectedConsumable: row })),
}))
