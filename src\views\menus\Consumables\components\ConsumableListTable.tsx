import { useMemo, useState } from 'react'
import Tooltip from '@/components/ui/Tooltip'
import DataTable from '@/components/shared/DataTable'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import useConsumableList from '../hooks/useConsumableList'
import cloneDeep from 'lodash/cloneDeep'
import { TbPencil, TbTrash } from 'react-icons/tb'
import type { OnSortParam, ColumnDef, Row } from '@/components/shared/DataTable'
import type { Consumables } from '../types'
import type { TableQueries } from '@/@types/common'
import { Tag } from '@/components/ui'
import ConsumableModal from './ConsumableModal'

const ActionColumn = ({
    onEdit,
    onDelete,
}: {
    onEdit: () => void
    onDelete: () => void
}) => {
    return (
        <div className="flex items-center justify-center gap-3">
            <Tooltip title="Edit">
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onEdit}
                >
                    <TbPencil />
                </div>
            </Tooltip>
            <Tooltip title="Delete">
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onDelete}
                >
                    <TbTrash />
                </div>
            </Tooltip>
        </div>
    )
}

const statusColor: Record<string, string> = {
    معتمد: 'bg-emerald-200 dark:bg-emerald-200 text-gray-900 dark:text-gray-900',
    'غير معتمد': 'bg-red-200 dark:bg-red-200 text-gray-900 dark:text-gray-900',
}

const ConsumableListTable = () => {
    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)
    const [toDeleteId, setToDeleteId] = useState('')
    const [editModalOpen, setEditModalOpen] = useState(false)
    const [editingConsumable, setEditingConsumable] = useState<
        Consumables | undefined
    >()

    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
    }

    const handleDelete = (consumable: Consumables) => {
        setDeleteConfirmationOpen(true)
        setToDeleteId(consumable.id)
    }

    const handleEdit = (consumable: Consumables) => {
        setEditingConsumable(consumable)
        setEditModalOpen(true)
    }

    const handleEditModalClose = () => {
        setEditModalOpen(false)
        setEditingConsumable(undefined)
    }

    const handleEditSuccess = () => {
        // Refresh the list after successful update
        mutate()
    }

    const handleConfirmDelete = () => {
        const newConsumableList = consumableList.filter((consumable) => {
            return !(toDeleteId === consumable.id)
        })
        setSelectAllConsumable([])
        mutate(
            {
                list: newConsumableList,
                total: consumableListTotal - selectedConsumable.length,
            },
            false,
        )
        setDeleteConfirmationOpen(false)
        setToDeleteId('')
    }

    const {
        consumableList,
        consumableListTotal,
        tableData,
        filterData,
        isLoading,
        setTableData,
        setSelectAllConsumable,
        setSelectedConsumable,
        selectedConsumable,
        mutate,
    } = useConsumableList()

    const allColumns: ColumnDef<Consumables>[] = useMemo(
        () => [
            {
                header: 'Type',
                accessorKey: 'category',
                cell: (props) => {
                    const row = props.row.original
                    return <span className=" heading-text">{row.category}</span>
                },
            },
            {
                header: 'Description',
                accessorKey: 'description',
            },
            {
                header: 'N',
                accessorKey: 'count',
                cell: (props) => {
                    const row = props.row.original
                    return <span className=" heading-text">{row.count}</span>
                },
            },
            {
                header: 'Warehouse',
                accessorKey: 'wareHouse',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className=" heading-text">{row.wareHouse}</span>
                    )
                },
            },
            {
                header: 'Status',
                accessorKey: 'status',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <div className="flex items-center justify-center">
                            <Tag className={statusColor[row.status]}>
                                <span className="capitalize">{row.status}</span>
                            </Tag>
                        </div>
                    )
                },
            },
            {
                header: 'Assigned By',
                accessorKey: 'assignedBy',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className=" heading-text">{row.assignedBy}</span>
                    )
                },
            },
            {
                header: 'Attachments',
                accessorKey: 'attachments',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className=" heading-text">{row.attachments}</span>
                    )
                },
            },
            {
                header: 'Created At',
                accessorKey: 'createdAt',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className=" heading-text">{row.createdAt}</span>
                    )
                },
            },
            {
                header: 'Edit',
                id: 'action',
                cell: (props) => (
                    <ActionColumn
                        onEdit={() => handleEdit(props.row.original)}
                        onDelete={() => handleDelete(props.row.original)}
                    />
                ),
            },
        ],
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [],
    )

    const columns: ColumnDef<Consumables>[] = useMemo(() => {
        const visibleColumns = filterData.visibleColumns || []
        return allColumns.filter((column) => {
            const key = (column as any).accessorKey || column.id
            return visibleColumns.includes(key) || key === 'action'
        })
    }, [allColumns, filterData.visibleColumns])

    const handleSetTableData = (data: TableQueries) => {
        setTableData(data)
        if (selectedConsumable.length > 0) {
            setSelectAllConsumable([])
        }
    }

    const handlePaginationChange = (page: number) => {
        const newTableData = cloneDeep(tableData)
        newTableData.pageIndex = page
        handleSetTableData(newTableData)
    }

    const handleSelectChange = (value: number) => {
        const newTableData = cloneDeep(tableData)
        newTableData.pageSize = Number(value)
        newTableData.pageIndex = 1
        handleSetTableData(newTableData)
    }

    const handleSort = (sort: OnSortParam) => {
        const newTableData = cloneDeep(tableData)
        newTableData.sort = sort
        handleSetTableData(newTableData)
    }

    const handleRowSelect = (checked: boolean, row: Consumables) => {
        setSelectedConsumable(checked, row)
    }

    const handleAllRowSelect = (checked: boolean, rows: Row<Consumables>[]) => {
        if (checked) {
            const originalRows = rows.map((row) => row.original)
            setSelectAllConsumable(originalRows)
        } else {
            setSelectAllConsumable([])
        }
    }

    return (
        <>
            <DataTable
                selectable
                columns={columns}
                data={consumableList}
                noData={!isLoading && consumableList.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={isLoading}
                pagingData={{
                    total: consumableListTotal,
                    pageIndex: tableData.pageIndex as number,
                    pageSize: tableData.pageSize as number,
                }}
                checkboxChecked={(row) =>
                    selectedConsumable.some(
                        (selected) => selected.id === row.id,
                    )
                }
                cellBorder={true}
                onPaginationChange={handlePaginationChange}
                onSelectChange={handleSelectChange}
                onSort={handleSort}
                onCheckBoxChange={handleRowSelect}
                onIndeterminateCheckBoxChange={handleAllRowSelect}
            />
            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title="Remove Consumables"
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDelete}
            >
                <p>
                    {' '}
                    Are you sure you want to remove this Consumable? This action
                    can&apos;t be undo.{' '}
                </p>
            </ConfirmDialog>

            <ConsumableModal
                isOpen={editModalOpen}
                onClose={handleEditModalClose}
                editData={editingConsumable}
                onSuccess={handleEditSuccess}
            />
        </>
    )
}

export default ConsumableListTable
